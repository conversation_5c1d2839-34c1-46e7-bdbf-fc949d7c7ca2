Title: Associate File Types to Dataset Types
URL: https://openbis.readthedocs.io/en/20.10.0-11/user-documentation/general-admin-users/admins-documentation/associate-file-types-to-dataset-types.html
Source: openbis
---

Associate File Types to Dataset Types

It is possible to associate given file types to given
Dataset
types
.
In this way when a file of this type is uploaded, the
Dataset
type
is automatically selected. This option can be found in
Settings
.
For example, a Jupyter notebook, which has extension
.ipynb
can
always be associated with the
Dataset
type
Analysis Notebook
.
Go to
Settings
Click
Edit
Scroll down to the
Dataset types for filenames
section
Enter the file extension (e.g. ipynb) in the
Filename extension
field
Select the D
ataset type
with which you always want to associate
this file type (e.g. Analysis Notebook)
Save
Updated on November 30, 2022