<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DeSi - Documentation Assistant</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            background-color: #2c3e50;
            color: white;
            padding: 1rem;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .header h1 {
            margin-bottom: 0.5rem;
        }

        .header p {
            opacity: 0.8;
            font-size: 0.9rem;
        }

        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            max-width: 800px;
            margin: 0 auto;
            width: 100%;
            padding: 1rem;
        }

        .messages {
            flex: 1;
            overflow-y: auto;
            padding: 1rem;
            background: white;
            border-radius: 8px;
            margin-bottom: 1rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .message {
            margin-bottom: 1rem;
            padding: 0.75rem;
            border-radius: 8px;
            max-width: 80%;
        }

        .message.user {
            background-color: #3498db;
            color: white;
            margin-left: auto;
        }

        .message.assistant {
            background-color: #ecf0f1;
            color: #2c3e50;
        }

        .message.system {
            background-color: #f39c12;
            color: white;
            text-align: center;
            max-width: 100%;
            font-size: 0.9rem;
        }

        .input-container {
            display: flex;
            gap: 0.5rem;
        }

        .message-input {
            flex: 1;
            padding: 0.75rem;
            border: 2px solid #bdc3c7;
            border-radius: 8px;
            font-size: 1rem;
            outline: none;
        }

        .message-input:focus {
            border-color: #3498db;
        }

        .send-button {
            padding: 0.75rem 1.5rem;
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            transition: background-color 0.2s;
        }

        .send-button:hover {
            background-color: #2980b9;
        }

        .send-button:disabled {
            background-color: #bdc3c7;
            cursor: not-allowed;
        }

        .controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding: 0.5rem;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .control-button {
            padding: 0.5rem 1rem;
            background-color: #95a5a6;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: background-color 0.2s;
        }

        .control-button:hover {
            background-color: #7f8c8d;
        }

        .stats {
            font-size: 0.8rem;
            color: #7f8c8d;
        }

        .session-stats {
            font-size: 0.8rem;
            color: #27ae60;
            font-weight: 500;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 1rem;
            color: #7f8c8d;
        }

        .loading.show {
            display: block;
        }

        @media (max-width: 600px) {
            .chat-container {
                padding: 0.5rem;
            }
            
            .message {
                max-width: 95%;
            }
            
            .controls {
                flex-direction: column;
                gap: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>DeSi</h1>
        <p>Your AI assistant for openBIS and Data Store documentation</p>
    </div>

    <div class="chat-container">
        <div class="controls">
            <div class="stats" id="stats">Loading...</div>
            <div class="session-stats" id="sessionStats">Session: Loading...</div>
            <button class="control-button" onclick="newSession()">New Session</button>
            <button class="control-button" onclick="clearSession()">Clear Memory</button>
        </div>

        <div class="messages" id="messages">
            <div class="message system">
                Welcome to DeSi! I can help you with questions about openBIS and Data Store operations.
                Ask me anything about documentation, procedures, or how to use these systems.
            </div>
        </div>

        <div class="loading" id="loading">
            DeSi is thinking...
        </div>

        <div class="input-container">
            <input type="text" class="message-input" id="messageInput" 
                   placeholder="Ask me about openBIS or Data Store..." 
                   onkeypress="handleKeyPress(event)">
            <button class="send-button" id="sendButton" onclick="sendMessage()">Send</button>
        </div>
    </div>

    <script>
        let isLoading = false;

        // Load stats on page load
        window.onload = function() {
            loadStats();
            loadSessionStats();
        };

        function loadStats() {
            fetch('/api/stats')
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        document.getElementById('stats').textContent = 'Stats unavailable';
                    } else {
                        const totalChunks = data.total_chunks || 0;
                        const sources = data.source_distribution || {};
                        const sourceText = Object.entries(sources)
                            .map(([source, count]) => `${source}: ${count}`)
                            .join(', ');
                        document.getElementById('stats').textContent = 
                            `Database: ${totalChunks} chunks (${sourceText})`;
                    }
                })
                .catch(error => {
                    console.error('Error loading stats:', error);
                    document.getElementById('stats').textContent = 'Stats unavailable';
                });
        }

        function loadSessionStats() {
            fetch('/api/session-stats')
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        document.getElementById('sessionStats').textContent = 'Session: unavailable';
                    } else {
                        const stats = data.stats;
                        if (stats.session_id) {
                            document.getElementById('sessionStats').textContent =
                                `Session: ${stats.total_messages} msgs, ${stats.total_tokens} tokens`;
                        } else {
                            document.getElementById('sessionStats').textContent = 'Session: New session';
                        }
                    }
                })
                .catch(error => {
                    console.error('Error loading session stats:', error);
                    document.getElementById('sessionStats').textContent = 'Session: unavailable';
                });
        }

        function handleKeyPress(event) {
            if (event.key === 'Enter' && !isLoading) {
                sendMessage();
            }
        }

        function sendMessage() {
            if (isLoading) return;

            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message) return;

            // Add user message to chat
            addMessage(message, 'user');
            
            // Clear input and show loading
            input.value = '';
            setLoading(true);

            // Send message to server
            fetch('/api/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ message: message })
            })
            .then(response => response.json())
            .then(data => {
                setLoading(false);
                
                if (data.error) {
                    addMessage(`Error: ${data.error}`, 'system');
                } else {
                    addMessage(data.response, 'assistant');
                    // Update session stats after successful message
                    loadSessionStats();
                }
            })
            .catch(error => {
                setLoading(false);
                console.error('Error:', error);
                addMessage('Sorry, I encountered an error. Please try again.', 'system');
            });
        }

        function addMessage(text, type) {
            const messagesContainer = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.textContent = text;
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        function setLoading(loading) {
            isLoading = loading;
            const loadingDiv = document.getElementById('loading');
            const sendButton = document.getElementById('sendButton');
            const messageInput = document.getElementById('messageInput');
            
            if (loading) {
                loadingDiv.classList.add('show');
                sendButton.disabled = true;
                messageInput.disabled = true;
            } else {
                loadingDiv.classList.remove('show');
                sendButton.disabled = false;
                messageInput.disabled = false;
                messageInput.focus();
            }
        }

        function newSession() {
            if (isLoading) return;

            fetch('/api/new-session', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                // Clear messages
                const messagesContainer = document.getElementById('messages');
                messagesContainer.innerHTML = `
                    <div class="message system">
                        New session started! I can help you with questions about openBIS and Data Store operations.
                    </div>
                `;
                // Update session stats
                loadSessionStats();
            })
            .catch(error => {
                console.error('Error starting new session:', error);
                addMessage('Error starting new session. Please refresh the page.', 'system');
            });
        }

        function clearSession() {
            if (isLoading) return;

            if (!confirm('Are you sure you want to clear the conversation memory? This cannot be undone.')) {
                return;
            }

            fetch('/api/clear-session', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    addMessage(`Error: ${data.error}`, 'system');
                } else {
                    addMessage('Conversation memory cleared successfully.', 'system');
                    // Update session stats
                    loadSessionStats();
                }
            })
            .catch(error => {
                console.error('Error clearing session:', error);
                addMessage('Error clearing session memory. Please try again.', 'system');
            });
        }
    </script>
</body>
</html>
