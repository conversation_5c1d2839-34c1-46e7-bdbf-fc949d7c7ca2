Title: How-to Guides
URL: https://datastore.bam.de/en/How_to_guides
Source: datastore
---

/
How_to_guides
How-to Guides
Page Contents
How to use the Data Store - main functions for Users
How to start
Register data in the Lab Notebook
Connect Experimental Steps in the Lab Notebook
Edit, Delete and Move functions
Register data in the Inventory
Connect Inventory Objects
Manage Storage of Objects
Barcode
Additional Function
Export data
Customize Group Settings - Admin/Data Store Stewards
Group settings
Templates
Object types
Parents and Children sections
Storage for Objects
Onboarding Data Store Stewards
Login training instance
Share Code in BAM research GitHub
Last edited by
<PERSON><PERSON><PERSON><PERSON>, Caroline
08/20/2025
Here you find an answer to "How do I …?" related questions on how to use the BAM Data Store and its  underlying software - openBIS. These goal-oriented instructions should help you accomplish specific tasks.  If the function you are looking for is missing, please contact the Data Store Team at
<EMAIL>
.
¶
How to use the Data Store - main functions for Users
¶
How to start
Log in to the BAM Data Store - main instance
Represent research data - Conceptual data model
Manage Access to Spaces and Projects
¶
Register data in the
Lab Notebook
Register a Project
Register Collection of the type Default Experiment
Register non-sequential Experimental Steps
Register sequential Experimental Steps
Upload data
¶
Connect Experimental Steps in the
Lab Notebook
Define Parents and Children of Experimental Steps
Display connections of Experimental Steps - Hierarchy Graph
Add multiple Children to an Experimental Steps - Children generator
¶
Edit, Delete and Move functions
Project Overview
Edit Projects, Collections, Objects and Datasets
Delete Projects, Collections, Objects and Datasets
Revert deletions of Collection, Objects and Datasets
Move Projects, Collections, Objects and Datasets
Move Experimental Step - Object with descendants
History of Changes
¶
Register data in the
Inventory
Register Projects
Register Collections
Register Objects
Batch Registration of Objects
Batch Update of Objects
¶
Connect
Inventory
Objects
Connect Inventory Objects with Experimental Steps from Lab Notebook
Define Parents and Children of Inventory Objects
¶
Manage Storage of Objects
Allocate Storage position to an Object
Batch registration/update of storage position(s)
Verify Storage Position
Delete Storage Position
¶
Barcode
Use Barcodes and QR Codes
Print Barcodes or QR codes
Scan Barcodes and QR Codes
¶
Additional Function
Embedding Images in Text Fields
Filter Objects within a Collection
Search - Global and Advanced search across all fields of all Entities
Search for: Objects in the Inventory
Search for: Objects - Experimental Steps in the Lab Notebook and save search queries
Use saved search queries
Search for Datasets
¶
Export data
Export (meta)data to file
Export data to Zenodo
¶
Customize Group Settings - Admin/Data Store Stewards
¶
Group settings
Customize the Main menu
Enable Barcodes
¶
Templates
Create Templates for Experimental Steps and other Objects
¶
Object types
Enable Object Types in drop-downs
¶
Parents and Children sections
Customize Parents and Children sections in Object Forms
Add a hint in Parents and Children sections
¶
Storage for Objects
Enable Storage Widget on Object Form
Configure Storage of Objects
¶
Onboarding Data Store Stewards
¶
Login training instance
Log in to the BAM Data Store-training instance
Checklist Group Settings customization
Checklist Use Case implementation
¶
Share Code in BAM research GitHub
Share Code in BAM research GitHub