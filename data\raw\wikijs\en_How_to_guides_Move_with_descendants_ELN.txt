Title: Move Experimental Step - Object with descendants
URL: https://datastore.bam.de/en/How_to_guides/Move_with_descendants_ELN
Source: datastore
---

/
How_to_guides
/
Move_with_descendants_ELN
Move Experimental Step - Object with descendants
Last edited by
<PERSON><PERSON><PERSON><PERSON>, Caroline
06/06/2025
An Object can also be moved together with its descendant Objects (i.e. children, grandchildren, etc.) if all Objects (Parent and Children) belong to one and the same Collection of the Object to be moved. To do this, click on the Object in the Collection, the
Object
Form opens, open the
More
drop-down menu, and select
Move
. Select the option
move the Object with all descendants
and start typing the
Name
of the (existing) Collection to which you want to move the Objects, review, and click
Accept
.
Select Experimental Step – Object with descendants
Open More drop-down menu
Select Move
Select option (to move all descendants)
Enter Code or Name of the target Collection in the search field
Check the box
Click Accept.