Title: Batch Update of Objects
URL: https://datastore.bam.de/en/How_to_guides/Batch_update_Inventory
Source: datastore
---

/
How_to_guides
/
Batch_update_Inventory
Batch Update of Objects
Last edited by
<PERSON><PERSON>, Angela
08/07/2025
Multiple
Objects
can be updated in a
Collection
using an Excel Template. Navigate to the relevant
Collection
(in the Inventory or Lab Notebook) and click on the
COLUMNS
tab in the Collection form. Select the
Identifier
of the Properties you want to update. If you have multiple Objects, you can filter the table. To export the Excel table for the selected Properties, click on the
EXPORTS
tab and select
Import Compatible
(Yes);
Columns
(All) (default order);
Rows
(All) pages/Current page/Selected rows (depending on the Objects you want to export and update). Click
EXPORT
to download the table. Modify the file and save it. In the Object form, click the
More
drop-down menu and select
XLS Batch Update
. Upload the file and press
Accept
. To confirm batch update, navigate to updated
Collection
.
Select Collection
Click on Columns tab
Press show all
Click on Exports tab:
Select Import Compatible (Yes),
Columns(All) (default order),
Rows(All) Pages
Click on Export
Update the Excel Template file and save it
Click on More drop-down menu (Object form)
Select XLS Batch Update Objects
Upload the Excel Template file
Review the entries and Accept.