Title: Customise Inventory Of Protocols
URL: https://openbis.readthedocs.io/en/20.10.0-11/user-documentation/general-admin-users/admins-documentation/customise-inventory-of-protocols.html
Source: openbis
---

Customise Inventory Of Protocols

Create Collections of Protocols

Collections
are folders used to organise
Objects
in the
Methods
Inventory. In this case,
Objects
are protocols.
Collections
need to be created inside another folder, called
Project
, in the
Methods
Space
in the inventory.
For example, if we want to create a collection of lab protocols for
microscopy and Mass spec, we need to adopt the following steps:
Register a first
Project
folder called PROTOCOLS in the
Methods
Space
.
In the
Protocols
folder, you can register two additional
Collections called Microscopy Protocols and MS Protocols
The steps for the registration of the folders are the same as explained
in
Create Collections of
Materials
Updated on April 26, 2023
Enable Protocols in Settings

If a new
Object type
for a protocol is created by an
Instance admin
in the admin interface, it is advisable to set the
Object type
to
Protocol
in the
Settings
, under
Utilities
.
For this, follow the steps below:
Go to
Settings
Click
Edit
Scroll to the last section of the
Settings
:
Object Type
definitions Extension
Open the
Object type
corresponding to your protocol, e.g.
General Protocol
Select
Use as Protocol
Save
This is done to be able to create local copies of protocols from the
Inventory
inside an
Experiment
when writing
Experimental steps,
as
described in
How to use protocols in Experimental
Steps
Updated on April 26, 2023