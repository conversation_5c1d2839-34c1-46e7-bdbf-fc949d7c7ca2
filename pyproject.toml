[project]
name = "DeSi"
version = "0.1.0"
description = "A RAG-focused chatbot that provides intelligent assistance for openBIS and Data Store documentation. It sources information from two distinct knowledge bases: ReadTheDocs (openBIS documentation) and Wiki.js (Data Store wiki), using a vector database for efficient retrieval."
readme = "README.md"
requires-python = ">=3.8"
dependencies = [
    "beautifulsoup4>=4.13.0",
    "langchain-core>=0.3.60",
    "langchain-ollama>=0.3.3",
    "langgraph>=0.2.0",
    "langgraph.checkpoint.sqlite>=2.0.10",
    "numpy>=2.2.6",
    "pandas>=2.2.3",
    "python-dotenv>=1.1.0",
    "requests>=2.32.0",
    "scikit-learn>=1.6.0",
    "flask>=3.1.0",
    "flask-cors>=6.0.1",
    "chromadb>=0.4.0",
]

[project.optional-dependencies]
eval = [
]

[project.scripts]

