Title: Export data to Zenodo
URL: https://datastore.bam.de/en/How_to_guides/Export_to_zenodo
Source: datastore
---

/
How_to_guides
/
Export_to_zenodo
Export data to Zenodo
Last edited by
<PERSON><PERSON><PERSON><PERSON>, Caroline
08/20/2025
To export data to Zenodo, you need a Zenodo account to generate a personal access token. To do this, log in to your Zenodo account, select
Settings
,
Applications
and copy the Zenodo token.  Return to openBIS, select
Utilities
and then
User Profile
and copy the
Zenodo API token
.
Select then
Exports
and
Export to Zenodo
from
Utilities
, enter the
title of the submission (*)
, select the files to be exported, and click
Export Selected
. The selected data will then be transferred to Zenodo as a ZIP file. The files exported to Zenodo can be private or public, before publishing, check the entries and ensure that no personal data has been exported, authors are listed correctly, and the project's data policy is being followed.
Select Utilities
Select Export
Select Export to Zenodo
Enter title of the submission
Click on Export Selected