Title: Customise Inventory Of Materials And Samples
URL: https://openbis.readthedocs.io/en/20.10.0-11/user-documentation/general-admin-users/admins-documentation/customise-inventory-of-materials-and-samples.html
Source: openbis
---

Customise Inventory Of Materials And Samples

Create Collections of Materials

Collections
are folders used to organise
Objects
in the
Materials
Inventory. Such
Objects
can be different types of
samples and materials (e.g. chemicals, antibodies, batteries,
environmental samples).
Collections
need to be created inside another folder, called
Project
, in the
Materials
inventory.
For example, if we want to create a collection of raw samples, we need
to adopt the following steps:
Create an
Object
type
called Sample. This can only be done by an
Instance admin
, from the admin interface, as explained here:
New Entity Type Registration
Create a first folder called Samples inside the
Materials
folder
(Project)
Create a second folder called
Raw Samples (Collection)
Create the Project folder

To create the
Project
folder:
Click on the
Materials
folder
Click the
+ New Project
button in the form.
Provide a description, if wanted. This is not mandatory.
Enter the
Code
. This will be the name of the folder, in this case SAMPLES. Codes only take alphanumeric characters and no spaces.
Create the Collection folder

To register the
Collection
folder, inside the
Project
folder:
Click on the
Project
folder, in this case
Samples
.
Click the
+ New
button in the main form and choose
Collection
from the dropdown.
Replace the automatically generated
Code
with something pertinent to the collection (e.g RAW_SAMPLES)
Fill in the
Name
field (e.g. Raw Samples). Note that by default, the navigation menu on the left shows the name. If the name is not provided, the code is shown.
Select the
Default object type
from the list of available types. This is the
Object
for which the
Collection
is used. In this case,
Sample
.
Select the
Default collection view
(see
Customise Collection View
)
Add the “+Object type” button in the Collection percentage

If you use a Collection for one Object type, you can display a button to add that type to the Collection, as shown below.
For this you need to edit the Collection form and set the Default Object type, as shown below.
Delete Collections

To delete an existing Collection:
Select
Edit Collection
under the
More..
dropdown menu
Select
Delete
under the
More..
drop down menu
Enable Storage Widget on Sample Forms

When a new
Object type
is created by an
Instance admin
(see
New Entity Type Registration)
, the storage widget is disabled by default.
If we want to track storage positions for this particular
Object type
as described in
Allocate storage positions to samples
, the
Storage
should be enabled in the
Settings
, under
Utilities
. This can be done by a
group admin
.
For this, follow the steps below:
Go to
Settings
, under
Utilities
Click the
Edit
button
Scroll to the last section of the Settings:
Object Type definitions Extension
Open the
Object type
for which you want to enable the storage, e.g.
Sample
Select
Enable Storage
Save
Configure Lab Storage

Fridges and freezers can be configured in the
Settings
, under
Utilities
.
Go to
Settings
Click
Edit
Scroll down to the
Storages
section
Click the
+ New Storage
button above the storage table, as shown below.
Fill in the
Storage Form
as explained below
How to fill in Storage Form:
Code
. It is advisable to provide a meaningful code for the storage, rather than using the default, because this information is needed when registering storage positions in Batch mode. For example MINUS80_ROOM_A1
Name
. The name is what is shown in most parts of the ELN. E.g. Minus 80°C in Room A1
Number of rows
. This is the number of shelves.
Number of columns
. This is the number of racks per shelf.
Allowed number of boxes in a rack
. This is the maximum number per rack. Enter a very high number if this is not important.
Rack space warning
. Enter space as percentage. E.g. 80, means that the system will give a warning when 80% of a rack is occupied.
Box space warning
. Enter space as percentage. E.g. 80, means that the system will give a warning when 80% of a box is occupied.
Validation level
. This is the minimum level of information required about the storage:
Rack validation
. The position in the shelf and rack needs to be specified.
Box validation
. In addition to
a
, a box name needs to be specified.
Box position validation
. In addition to
a
and
b
, the position in the box needs to be specified.
Add metadata to Storage Positions

Storage positions by default have the following metadata:
Storage code
Storage rack row
Storage rack column
Box name
Box size
Box position
User id
It is possible to add additional information. This can be done by an
Instance Admin
by editing the
Object
Type
STORAGE_POSITION
in
the admin interface (see
New Entity Type Registration
).