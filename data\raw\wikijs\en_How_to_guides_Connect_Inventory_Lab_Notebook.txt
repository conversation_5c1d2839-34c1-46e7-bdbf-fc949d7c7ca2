Title: Connect Objects of Inventory with Experimental Steps from  Lab Notebook
URL: https://datastore.bam.de/en/How_to_guides/Connect_Inventory_Lab_Notebook
Source: datastore
---

/
How_to_guides
/
Connect_Inventory_Lab_Notebook
Connect Objects of Inventory with Experimental Steps from  Lab Notebook
Last edited by
<PERSON><PERSON>, Angela
08/07/2025
To connect an Inventory Object to an Experimental Step, select the relevant inventory Object and click on the
Edit
tab. In the
Parent
and
Children
section, click
Search Any
and select
Experimental step
from the
Select an object type
drop-down menu. You will be returned to the
Update-Object
form. Enter the
Code or Name of the Object
in the text box that will appear below the Parents section. Start typing the Code or Name of the Parent-Object to display available Objects for your group, select the appropriate Object and
Save
.
To display linked Objects in a hierarchy graph, navigate to the edited Inventory item, click the
More
drop-down menu, and select
Hierarchy Graph
.
It is also possible to define multiple Parents and Children to  Objects at the same time. To do this, use the
Paste Any
option, enter the Code or Name of respective Objects, review the entries and
Save
. You can copy the Code or Name of Objects from another ELN page (Log in to the BAM Data Store in another/private browser window). Paste the Codes(s) or name(s) in the text fields, review and
Save
.
Select Object in the Inventory
Click on Edit tab
Navigate to the Parent and Children sections
Click Search Any
Select Object Type - Experimental Step
Enter Code or Name of Experimental Step to connect
Review the entries and Save.