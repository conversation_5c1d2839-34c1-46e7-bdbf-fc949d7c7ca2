Title: Customise the Main Menu
URL: https://openbis.readthedocs.io/en/20.10.0-11/user-documentation/general-admin-users/admins-documentation/customise-the-main-menu.html
Source: openbis
---

Customise the Main Menu

Main Menu Sections

The main menu can be customised from the
Settings
, under
Utilities
, to hide sections that are not needed by the lab.
Go to
Settings.
Click
Edit.
Go to the
Main Menu
section.
Disable the parts of the menu which are not needed.
Save.
showLabNotebook
: if unselected, the
Lab Notebook
section of
the main menu (
Lab
Notebook)
will be hidden.
showInventory
: if unselected, the
Inventory
section of the
main menu (
Inventory of Materials and
Methods
)
will be hidden.
showStock
: if unselected, the
Stock
section of the main menu
(
Managing Lab Stocks and
Orders
)
will be hidden.
showObjectBrowser
: if unselected, the
Object Browser
under
Utilities
in the main menu (
Object
Browser)
will be hidden.
showStorageManager
: if unselected, the
Storage Manager
under
Utilities
in the main menu
(Storage
manager
)
will be hidden.
showAdvancedSearch
: if unselected, the
Advanced Search
under
Utilities
in the main menu (
Advanced
Search)
be hidden.
showUnarchivingHelper
: if unselected, the
Unarchiving Helper
and
Archiving Helper
under
Utilities
in the main menu (
Data
archiving)
will be hidden.
showTrashcan
: if unselected, the
Traschcan
under
Utilities
in the main menu
(
Trashcan
)
will be hidden.
showVocabularyViewer:
if unselected, the
Vocabulary
Browser
under
Utilities
in the main menu (
Vocabulary
browser
)
will be hidden.
showUserManager
: if unselected, the
User Manager
under
Utilities
in the main menu (
User
Registration
)
will be hidden.
showUserProfile
: if unselected, the
User Profile
under
Utilities
in the main menu (
User
Profile
)
will be hidden.
showZenodoExportBuilder
: if unselected, the
Zenodo
Export
under
Utilities -> Exports
in the main menu 
(
Export to
Zenodo
)
will be hidden.
showBarcodes
: if unselected, the
Barcodes/QR codes Generator
under
Utilities
in the main menu
(
Barcodes)
will be hidden.
Lab Notebook menu

It is also possible to customise which entities should be shown under
Experiments/Collections
in the main menu under the
Lab Notebook
section.
By default, only the
Object
types
Entry
and
Experimental Step
are shown (see picture below).
If you want to show additional custom
Object
types in the lab notebook
main menu, they need to be enabled by editing the
Settings
.
Go to the
Object Type definitions Extension
section in the
Settings
. Open the relevant
Object
type, which you would like to
see in the Main menu of the Lab Notebook and select
Show in lab
notebook main menu
, as shown below.
By default, the Object Types
Entry
and
Experimental Step
have
this option already selected.
Please note that this is only valid for the
Lab Notebook
section. In
the
Inventory
section, entries are never shown in the main menu,
because inventories can potentially have thousands of entries, which are
better visualised in tables, rather than in the main menu.
Updated on April 26, 2023