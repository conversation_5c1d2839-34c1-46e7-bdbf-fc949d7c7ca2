Title: Embedding Images in Text Fields
URL: https://datastore.bam.de/en/How_to_guides/Embedding_Images
Source: datastore
---

/
How_to_guides
/
Embedding_Images
Embedding Images in Text Fields
Last edited by
<PERSON><PERSON><PERSON><PERSON>, Caroline
06/24/2025
To embed an image (.jpeg, .png formats) in the description of the Entity during the editing, drag an image in the description field. Another way is to click on the picture icon (
) in the rich text editor field. Alternatively, the image (.jpeg, .png, .pdf, .svg formats) could be embedded as an
ELN Preview Dataset
. For that, select relevant
Default Experiment (Collections)
or
Object
, and
upload
the Dataset. In the
Create Dataset
form, in the Data Set Type (*) drop-down menu select
ELN Preview
. Fill out the relevant information and click on select files to upload, review the files and
Save
.
Select Default Experiment or Object
Click Upload button
Select ELN Preview in the Dataset Type (*)
Select files to upload
Review the files and Save.