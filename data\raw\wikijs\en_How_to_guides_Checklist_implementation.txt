Title: Checklist Use Case implementation
URL: https://datastore.bam.de/en/How_to_guides/Checklist_implementation
Source: datastore
---

/
How_to_guides
/
Checklist_implementation
Checklist Use Case implementation
Page Contents
Example 1
🎯 Define Realistic Goals
📊 Select Data to Represent
🗂️ Represent research data in the Data Store - Create a Conceptual Model
📝 Create Templates for Experimental Steps
🚀 Use Case Implementation
🧪 Define Lab Storage
🔍 Define Search Queries
Last edited by
<PERSON><PERSON>, Angela
07/25/2025
¶
Example 1
This checklist contains implementation steps that DSSt should consider in order to implement a use case for the division with the goal to improve the discoverability and reusability of research data and simplify the use of Data Store for users.
¶
🎯 Define Realistic Goals
Establish clear and realistic objectives for using the Data Store.
This check list focuses on improving findability and reusability of research data.
¶
📊 Select Data to Represent
Choose new research data.
Choose structured, tabular data that is easily accessible.
¶
🗂️ Represent research data in the Data Store - Create a Conceptual Model
Use the
Represent_research_data
.  Use the
template
in (
draw.io
) to Identify key steps and data points in the workflow; map data in the folder structure for the Data Store and define parent-child relationships between objects.
Manage roles and rights at the Space and Project level to comply with agreements and contracts of related research projects.
¶
📝 Create Templates for Experimental Steps
Develop templates that include sufficient information to describe datasets attached to experimental steps.
¶
🚀 Use Case Implementation
Automate metadata import from instruments (e.g., generate barcodes).
Automate metadata import from measurements (e.g., structured tabular data, reuse templates for experimental steps).
Batch register objects such as inventory items and samples.
¶
🧪 Define Lab Storage
Set up lab storage for samples and batch register/update samples.
Ensure storage locations are well-documented and are easily found.
¶
🔍 Define Search Queries
Create and save search queries to improve data traceability.
Ensure queries are easy to use and provide relevant results.