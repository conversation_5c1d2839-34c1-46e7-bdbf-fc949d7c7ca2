Title: Use case: EuVSOP
URL: https://datastore.bam.de/en/use_cases/EuVSOP
Source: datastore
---

/
use_cases
/
EuVSOP
Use case: EuVSOP
Page Contents
Use Case 1: Synthesis of europium‑doped VSOP, customized enhancer solution and improved microscopy fluorescence methodology for unambiguous histological detection
Conceptual Data Model
Map conceptual data model in the Data Store-openBIS data structure
Last edited by
<PERSON><PERSON><PERSON><PERSON>, <PERSON>
Last Monday at 9:11 AM
¶
Use Case 1: Synthesis of europium‑doped VSOP, customized enhancer solution and improved microscopy fluorescence methodology for unambiguous histological detection
The Eu-VSOP project investigates the unambiguous identification of iron oxide nanoparticles -VSOP doped with Europium (III) for flourescence detection in biological samples such as histological tissue sections.
Background
: VSOP are very small iron oxide nanoparticles used in magnetic resonance imaging (MRI). These nanoparticles are studied as an alternative to Gadolium based MRI-contrast agents due to their potentially lower toxicity. The clear detection of EuVSOP in tissue sections enables biodistribution studies.
Note that the content of this Demo Project is inspired by some scientific open access publications.
[1]
Some modifications might be included for illustration of openBIS functions.
¶
Conceptual Data Model
The
conceptual data model
describes the steps required to map research data in the Data Store and includes a guideline template to detail the following steps for a generic example.
¶
Draw the research data Workflow
¶
Identify Entities and Entity Types
¶
Map Entities in the openBIS data structure and Connect Entities
¶
Map conceptual data model in the Data Store-openBIS data structure
¶
Register data in the Lab Notebook:
Register a Project - EuVSOP
Register a Collection – Experimental Steps of the type Default Experimental Step
At the Collection level, register Objects of the Type -Experimental Step for EuVSOP- Synthesis, HEE Treatment.
At the Object level – EuVSOP HEE Treatment, register new Objects of the Type – Experimental Step for: Nanoparticle Iron quantification, Magnetic Characterization and Nanoparticle Size
Upload datasets to all Experimental Steps
Connect Experimental Steps: Edit EuVSOP Synthesis to define as Children the Experimental Step - EuVSOP HEE Treatment.
¶
Register items in the Inventory:
Use inventory spaces defined per default in division´s inventory space (e.g., X.1 Equipment and X.1 Materials).
Register a Project-folder - Instruments in the folder (Space) X.1 Equipment
Register Instruments
Register a Project folder - Consumables in the folder (Space) X.1 Materials
Register a Collection – Chemicals
Register Objects Chemicals with Batch update registration
¶
Connect Inventory and ELN -Experimental Steps:
Edit Experimental Steps (e.g., EuVSOP Synthesis) to define Objects such as Chemicals and Instruments as Parents.
Visualize Parent-child connections in the hierarchical tree of each Object (e.g., Experimental Step: EuVSOP Nanoparticle Size).  To improve the visibility of the levels in the hierarchical tree, the displayed types for Chemicals and Instruments are excluded from the hierarchical tree in openBIS.
doi:
10.1186/s12951-017-0301-6
.
↩︎