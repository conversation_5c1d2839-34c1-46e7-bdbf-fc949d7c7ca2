Title: Masterdata exports and imports
URL: https://openbis.readthedocs.io/en/20.10.0-11/user-documentation/general-admin-users/admins-documentation/masterdata-exports-and-imports.html
Source: openbis
---

Masterdata exports and imports

Since openBIS version 20.10.5 it is possible to export masterdata from
one openBIS instance and import it in another one via the admin UI.
Masterdata export

All types tables (
Object Types, Collection Types, Dataset Types,
Vocabulary Types, Property Types
) can be exported as shown below for
the
Object Types
.
When you export you have the following options:
Import Compatible
:
Yes
: in this case some columns which are incompatible
with imports (i.e. modification date) are not exported even
if selected; some columns that are required by openBIS for
imports are added to the exported file even if not selected.
No
: in this case all columns or selected columns are
exported.
Columns
:
All (default order)
. All columns are exported, in accordance
with the selection explained above for import compatibility.
Selected (shown order)
. Selected columns are exported, in
accordance with the selection explained above for import
compatibility.
Rows
: current page or all pages
Include dependencies
: yes or no. If you include dependencies,
all property types, vocabularies and associated objects are also
exported. Default is “yes”.
If the types have validation plugins or dynamic script plugins
associated with them, a zip file containing the scripts is exported from
openBIS.
Masterdata import

To import the file with the relevant masterdata that was exported as
explained above:
Go to the
Tools
section and select
Import -> All
from the
menu.
Upload the file you exported before using the
CHOOSE FILE
button.
Select one of the 3 possible options for the Update mode:
Fail if exists
: if some entries already exist in openBIS,
the upload will fail;
Ignore if exists
: if some entries already exist in openBIS,
they will be left untouched, even if their definition in the
file is different from the existing definition in openBIS;
Update if exists
: if some entries already exist in openBIS
and their definition in the file is different from the existing
definition in openBIS, they will be updated;
Since openBIS 20.10.6, the import of zip files is supported.
Masterdata version

In openBIS version 20.10.8, the masterdata version has been removed from the exported masterdata files.