Title: Add multiple Children to an Experimental Steps - Children generator
URL: https://datastore.bam.de/en/How_to_guides/Children_generator
Source: datastore
---

/
How_to_guides
/
Children_generator
Add multiple Children to an Experimental Steps - Children generator
Last edited by
<PERSON><PERSON><PERSON><PERSON>, Caroline
07/15/2025
The children generator function allows to register multiple children simultaneously during the registration of the Object or while editing it. At first, you need to register an Object of Type Experimental Step and defined at least one parent for it (e.g. instruments, chemicals, a sample, etc.).  The child generator will allow you to generate Children (e.g., subsequent Experimental Steps) with a defined combination of Parents.
To do this, select the
Experimental Step
with registered Parents. Click on the
Edit
tab, scroll down to the
Parents
and
Children
section in the Experimental Step form, and click on the
Generate Children
tab. A matrix of Parents is displayed in the
Children Generator
form. Select all Parents, choose the
Experimental Step
in the
Object type
drop-down menu and enter the number of replicates of newly generated Children, and click on
Generate
. Review the entries and
Save
.
Note, to ensure the traceability of the data in the Data Store, a minimum set of mandatory Properties has been defined for some Object Types. The child generator only allows the registration of Objects with
non-mandatory
Properties, as the Object form cannot be edited. It is therefore only possible to register multiple Objects of various Object Types with the child-parents relationship simultaneously using the
Batch registration
function.
Select Experimental Step with registered Parents
Click on Edit tab
Scroll down to the Parents and Children section
Click on Generate Children tab
Choose Experimental Step in the Object type drop-down menu
Enter the number of new child(ren)
Review and click on Generate