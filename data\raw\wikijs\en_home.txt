Title: Welcome to BAM Data Store
URL: https://datastore.bam.de/en/home
Source: datastore
---

Welcome to BAM Data Store
Page Contents
Welcome to the Data Store Wiki
Wiki Structure:
💡 Concepts
📖 How-to guides
❓ FAQ
👥 Use cases
What is the Data Store?
What is openBIS?
What is the Data Store Project?
What is the Data Store rollout process?
Last edited by
<PERSON><PERSON><PERSON><PERSON>, <PERSON>
Yesterday at 11:44 AM
¶
Welcome to the Data Store Wiki
This Wiki provides information on the BAM Data Store - the central system for research data management at the Bundesanstalt für Materialforschung und -prüfung (BAM).
The Wiki is not intended to replace the openBIS documentation by the ETHZ (
User docs
,
Admin docs
). It provides conscise guidance and should serve as an additional source of openBIS and Data Store documentation for BAM employees.
Some articles of this Wiki are currently under construction. If you have further questions that are not yet answered here, please contact
<EMAIL>
.
¶
Wiki Structure:
¶
💡 Concepts
Explanation about terms and concepts.
Explore Concepts
¶
📖 How-to guides
Step-by-step instructions for openBIS functions.
Go to Guides
¶
❓ FAQ
Frequently asked questions about Data Store and openBIS.
View FAQ
¶
👥 Use cases
Discover Use cases of the Data Store.
Discover Use cases
¶
What is the Data Store?
The Data Store is the central system for research data management (RDM) at BAM.
The Data Store is the central system for research data management (RDM) at BAM.
It enables divisions to digitally organize and describe laboratory inventory -such as instruments, samples, standard operating procedures (SOPs), using customizable metadata and linked documentation..
Integrated with electronic lab notebook (ELN), the Data Store allows experimental steps to be connected with inventory items, ensuring centralized and traceable documentation of research processes.  This structure linkage supports the FAIR principles
[1]
(Findable, Accessible, Interoperable, Reusable), facilitating both internal and external reuse of research data in line with funding bodies.
By storing data and metadata in a unified system, the Data Store enhances interdisciplinary collaboration and lays the foundation for advanced data analysis, including big data and Artificial Intelligence (AI) applications.
¶
What is openBIS?
openBIS (open Biology Information System) is the underlying platform of the Data Store.
It is an open-source software solution for Research Data Management (RDM) and Electronic Lab Notebook (ELN).
Developed and maintained by the Scientific IT Services (SIS) at ETHZ Zurich (ETHZ), openBIS was originally designed for life sciences
[2]
[3]
, it is now increasingly used materials science and other research domains.
openBIS provides a browser-based graphical user interface (GUI) for the managing digital laboratory inventories and documenting experiments in a standardized way.  Data files can be imported into via the GUI or through programming interfaces and linked to inventory items and experimental steps.
openBIS also supports integration with external tools and services, such as exporting data to
Zenodo
repository and analyzing scientific data in
Jupyter Notebook
.
For more information on openBIS visit the official website (
https://openbis.ch/
).
¶
What is the Data Store Project?
The introduction of a RDM system does not happen overnight.
To evaluate openBIS suitability, a pilot phase was conducted from 01.12.2020 to 28.02.2022. During this period, five BAM research groups from diverse domains successfully implemented openBIS, confirming its effectiveness for managing data in various materials science domains.
Following the pilot’s success, the Data Store project was approved to establish the system based on openBIS as a central RDM system across all BAM divisions. The project, led by VP.1 eScience and VP.2 Information Technology, began in October 2022 and is scheduled to run for 3.5 years.
The initial phase focused on developing the necessary IT infrastructure and preparing for the software rollout, included an analysis of RDM needs across BAM. The rollout of the Data Store began in 2023.
For more information about the Data Store project visit the BAM infoportal (
About the Project
)
¶
What is the Data Store rollout process?
Throughout all phases, project management and communication play a crucial role in supporting a smooth and efficient implementation.
The actual Data Store rollout began in May 2023 and is being carried out in successive phases, with several divisions being trained at the same time. Lessons learned are collected at the end of each rollout phase to implement improvements in subsequent rollout phases. The order of the rollout is determined based on the interest expressed by division heads in surveys done in December 2022 and February 2025.
The current onboarding concept lasts 2 to 3 months for assigned Data Store Stewards (DSSt), including 1 day for division heads and all employees working with research data.
The Data Store Stewards are one or two members of the division appointed by the division head. They are ideally permanently employed for sustainability and are familiar with inventory, experimental processes and the (digital) workflows of the division. DSSts are trained to use main functions and customize the group settings.
All other division members who handle research data receive introductory training in the use of the system. The head of division takes part in information events and coordinates the completion of the rollout phase together with the DSSts.
After the rollout, the heads of the division and the DSSts, together with the users, are responsible for storing newly generated research data and the continuous implementation of the Data Store within their division.
Mark D. Wilkinson et al. (2016). "The FAIR Guiding Principles for scientific data management and stewardship". Scientific Data. 3 (1): 160018. doi:
10.1038/SDATA.2016.18
.
↩︎
Angela Bauch et al. openBIS: a flexible framework for managing and analyzing complex data in biology research. 12, 468 (2011). doi:
10.1186/1471-2105-12-468
.
↩︎
Caterina Barillari et al. openBIS ELN-LIMS: an open-source database for academic laboratories. Bioinformatics. 32 (4), Feb 2016, 638–640. doi:[10.1093/bioinformatics/btv606].(
https://doi.org/10.1093/bioinformatics/btv606
).
↩︎