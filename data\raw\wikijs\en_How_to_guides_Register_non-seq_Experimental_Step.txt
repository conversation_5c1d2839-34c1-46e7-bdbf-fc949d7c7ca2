Title: Register non-sequential Experimental Steps
URL: https://datastore.bam.de/en/How_to_guides/Register_non-seq_Experimental_Step
Source: datastore
---

/
How_to_guides
/
Register_non-seq_Experimental_Step
Register non-sequential Experimental Steps
Last edited by
<PERSON><PERSON><PERSON><PERSON>, Caroline
06/24/2025
To register non-sequential Experimental steps, register a new
Object
at the Collection level (Default Experiment).
Navigate to relevant - Default Experiment, click the
+ New
tab, select
Experimental Step
in the Select an object type drop-down menu. Fill out the New Experimental Step form, display Identification Info (if hidden, open the More drop-down menu and select Show Identification Info).  The Code is automatically generated for Objects and can only be changed during registration. Give the Experimental Step a meaningful
Name
, as this will be displayed to the users, review the entries and
Save
.
Note that the new Experimental Step is organized at the same hierarchical level of Objects (
) in the Lab Notebook left-hand  menu.
To fill out the Experimental Step form with a
Template
predefined for the group by the Data Store Steward, click on the
Templates
tab in the New Experimental Step form, select
Template
, add information to the form as required, review the entries and
Save
. Reload the web page to see the changes.
Select Default Experiment
Click the + New tab
Select an Object Type - Experimental Step
Fill out the Experimental Step form
Alternatively, select relevant Template
click on Templates tab, select Template and complete relevant information
Review the entries and Save.