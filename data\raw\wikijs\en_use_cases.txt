Title: List of use cases
URL: https://datastore.bam.de/en/use_cases
Source: datastore
---

/
use_cases
List of use cases
Page Contents
openBIS Data Store Use Cases:
Sharing Use Cases in Wiki
Publication of BAM Use cases
Last edited by
<PERSON><PERSON>, <PERSON>
Last Monday at 5:01 PM
¶
openBIS Data Store Use Cases:
¶
Sharing Use Cases in Wiki
Data Store stewards are welcome to share an Use case of your division and make your work visible.
We provide a simplified example
EuVSOP
as inspiration for how you can illustrate a use case.  In addition, the benefits of the Data Store implementation for the group, project, etc. can be described.
Simply send us your text in any format (.docx, .txt, etc.) together with screenshots (.jpg, .png) by email (
<EMAIL>
).  We will implement your example in the wiki.
It is not about waiting for the perfect use case, but about exchanging ideas on how research MSE workflows can be mapped in openBIS Data Store. However, if you need your use case reviewed, please us at
<EMAIL>
.
EuVSOP
¶
Publication of BAM Use cases
Alternatively, you can also publish an openBIS use case in Zenodo and make it citable:
QI-Digital/publication Zenodo