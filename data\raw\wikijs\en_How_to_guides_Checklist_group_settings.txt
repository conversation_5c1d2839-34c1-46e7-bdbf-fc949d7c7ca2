Title: Checklist Group Settings Customization
URL: https://datastore.bam.de/en/How_to_guides/Checklist_group_settings
Source: datastore
---

/
How_to_guides
/
Checklist_group_settings
Checklist Group Settings Customization
Page Contents
✅ Group Settings Customization Checklist
Objects registration
Parent-child relationships
Barcodes
Lab Storage
Group ELN Settings
Last edited by
<PERSON><PERSON><PERSON><PERSON>, Caroline
06/19/2025
¶
✅ Group Settings Customization Checklist
This checklist helps you keep track of all group settings and configurations for your department that can be customized by the Data Store Steward.
¶
Objects registration
Enable Object Types in drop-down menus
Create Templates for Experimental Steps and other Objects
¶
Parent-child relationships
Customize Parents and Children sections in Object Forms
Add a hint in Parents and Children sections
¶
Barcodes
Enable Barcodes
¶
Lab Storage
Enable Storage for an Object Type
Configure Lab storage
¶
Group ELN Settings
Storages
Templates
Object Type definition extension
Inventory Spaces
Customize the Main menu
Miscellaneous
Optional:
Manage Access to Spaces and Projects - FB Inventory folders
Roles defined per default in the Data Store
Group settings can be updated adopt to the data policies of projects, when required.
Define naming conventions for entities (Project, Collections, Object, Data sets); templates, etc. to ensure consistency and facilitate findability.