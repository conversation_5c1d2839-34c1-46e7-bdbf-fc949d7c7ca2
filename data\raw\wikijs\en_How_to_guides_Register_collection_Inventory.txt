Title: Register Collections in the Inventory
URL: https://datastore.bam.de/en/How_to_guides/Register_collection_Inventory
Source: datastore
---

/
How_to_guides
/
Register_collection_Inventory
Register Collections in the Inventory
Last edited by
<PERSON><PERSON><PERSON><PERSON>, Caroline
06/16/2025
To register a
Collection
in the Inventory, navigate to relevant Project, click on
+ New
tab and select
Collection
from the Experiment/Collection type drop-down menu. The
Collection
form opens. Fill out the
Identification Info
(if hidden, open the
More
drop-down menu and select Show Identification Info). Enter the
Code
, a meaningful
Name
, select
Empty
in
Default object type
and
List
view in
Default collection view
drop-down menu.
Note that Collections can contain Objects of one or many types. Arrange Objects in the Inventory in a meaningful way for the group.
Objects can be moved together with descendants, only if they are in the same Collection (
Move Objects to a different Collection
).
Select Project
Click on + New tab
Select Collection from the Experiment/Collection type drop-down menu
Enter Code and Name
Select in Default Object Type – (empty)
Select in Default Collection view – List view
Review the entries and Save.