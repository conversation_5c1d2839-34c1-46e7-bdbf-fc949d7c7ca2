Title: Starting and Stopping the openBIS Application Server and Data Store Server
URL: https://openbis.readthedocs.io/en/20.10.0-11/system-documentation/standalone/starting-and-stopping.html
Source: openbis
---

Starting and Stopping the openBIS Application Server and Data Store Server

Start Server

The openBIS application server is started as follows:
prompt> <installation folder>/bin/bisup.sh
On startup the openBIS server creates the openBIS database (named
openbis_prod
by default) and checks the connection with the remote authentication services (if they are configured). Log files can be found in
<installation
folder>/servers/openBIS-server/jetty/logs
. Also the following command shows the log:
<installation
folder>/bin/bislog.sh
Warning
Unless otherwise configured through running the installation script or within the database itself, the first user logged in into the system will have full administrator rights (role
INSTANCE_ADMIN
).
Commonly, the application server is configured to access a local data store via the data store server. This has to be started after the AS:
prompt> <installation folder>/bin/dssup.sh
The application server and the data store server can also be started one after the other using a single command:
prompt> <installation folder>/bin/allup.sh
Stop Server

The application server is stopped as follows:
prompt> <installation folder>/bin/bisdown.sh
To only stop the data store server:
prompt> <installation folder>/bin/dssdown.sh
To stop both the data store server and then the applicaiton server:
prompt> <installation folder>/bin/alldown.sh