Title: Managing Storage Of Samples
URL: https://openbis.readthedocs.io/en/20.10.0-11/user-documentation/general-users/managing-storage-of-samples.html
Source: openbis
---

Managing Storage Of Samples

Allocate storage positions to samples

If we want to track the storage position of samples, openBIS provides a
graphical overview of lab storages.
Lab storages need to be configured by a
lab manager
or
group admin
,
as explained here:
Configure Lab
Storage
This can be done in two ways:
add storage information on the sample form during (or after) sample
registration
batch register storage positions for several samples
Register storage position for a single sample

1. Navigate to the
Storage
section, at the bottom of the sample
form. Click the
+ New Storage Positions
above the table, as shown
below:
In the widget that opens, select the appropriate
Storage
from the
dropdown menu. Storage must be configured by a lab manager or group
admin as explained in
Configure Lab
Storages
3. Select the
position
in the storage (shelf and rack).
4. If the sample is in a box, provide a
Box Name.
5. Select the
Box Size
form the list of configured sizes (the list
can be configured by an
Instance Admin)
.
6. Select the
Position
in the box.
7. Click
Accept.
Add additional metadata to storage positions

By default, the storage only keeps track of locations. If the
Storage
Position
has been configured by an
Instance admin
to have additional
metadata (e.g. freezing date), these can be added by clicking on the
link in the storage table, as shown below. The link becomes available
after saving the sample.
The additional information can be entered in the
Storage Position
Object
form.
Batch register storage positions

XLS Batch Registration

With the new XLS batch registration, samples and their storage positions
can be registered in one transaction using the XLS template file, as
explained in
Batch register entries in a
Collection
.
Batch Registration with TSV file

Storage positions are modelled in openBIS as children of other entries.
To register the positions for several samples with the Batch
Registration using the .tsv template, first the parent samples need to
be registered in openBIS. In a second step, the positions are assigned.
To assign storage positions in batch mode follow the steps below:
Select
Storage positions
from the
Batch Registration
drop
down menu.
Download the
template file
.
Remove the
identifier
column from the file (identifiers need
to be automatically generated by openBIS).
Fill in the
parents
column. These are the identifiers of the
samples for which we want to register the storage
positions(/MATERIALS/PROJECT/OBJECT_CODE).
Fill the remaining information about the storage positions.
Save the file and upload with the
Batch Registration
.
An example file can be found
here:
SAMPLE-STORAGE_POSITION-template
Updated on April 26, 2023
Batch update storage positions

To update several storage positions, we can use the batch update option
from the Object Browser:
Go to the
Object Browser
under
Utilities
in the main menu
Select the object type
Storage Position
from the dropdown menu
(see picture)
Use the table
Filter
to select the storage positions you want to
update
(see
Tables
)
Export the table (see
Tables
)
Edit the file to make the changes needed (e.g. change the name of a
box, change the storage, change a box position, change box size etc)
Select
XLS Batch Update Objects
from the
More..
dropdown.
7. Import the file you modified before and update the storage
positions.
Updated on April 25, 2023
Delete storage positions

Delete single storage positions

To delete a single storage position from a sample:
Edit the sample for which you want to deleted the storage position
Navigate to the
Storage
section at the end of the page
Use the “
–
” button in the
Storage Position
table, as shown
in the picture
Save the sample
Please note that the storage position deleted in this way is moved to
the trashcan. To delete the position permanently, this has to be deleted
from the trashcan (see
Trashcan
).
Remove one of multiple positions in the same box

If one sample has been assigned to multiple positions in the same box
and you need to remove only one or some of them, you can follow these
steps:
Edit
the sample for which you need to remove the storage
position in the box
Navigate to the
Storage
section at the end of the page
Click on the
table row
(see picture below)
Unselect
the position you want to remove (eg. A5 in the example below)
Click
Accept
Save
the sample
Delete multiple storage positions

To delete multiple storage positions from multiple samples we can use
the
Object Browser
.
Go to the
Object Browser
under
Utilities
in the main menu
Select
Storage Position
from the
Object Type
dropdown
3.
Filter
the table to find the storage positions you want to
delete
(see
Tables
)
4. Select the positions you want to delete from the table and click the
Delete
button (see picture below)
5. You will be asked to provide a reason for deletion
6. The deleted storage positions will be moved to the trashcan and
should be removed from there to be permanently deleted (see
Trashcan)
Updated on May 2, 2023
Overview of lab storages

The
Storage Manager
, under
Utilities
, provides an overview of
each single storage configured for the lab, by the lab admin.
Select the storage containing the samples to visualise from the
Storage
drop down menu.
Click on a box to view its content.
When hovering with the mouse over a sample inside a box, the info
about the sample is shown.
Overview of lab Storages

Change storage position of samples

The
Storage Manager
can also be used to move samples from one
storage position to another, if the location of the sample is changed:
Click on
Toggle Storage B
(see figure above).
Select the destination storage, from the
Storage
drop down
menu.
Drag and drop the box or sample to move from
Storage A
to the
desired position in
Storage B
. Please note that the move
operation for samples with multiple positions in the same box or
in different boxes is not supported.
Changes are visualised at the bottom of the page. To save them,
click
Save Changes
on top of the
Storage Manager
form.
Updated on April 25, 2023