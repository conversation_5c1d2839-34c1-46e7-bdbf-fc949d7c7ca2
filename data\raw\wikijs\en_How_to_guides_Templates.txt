Title: Create Templates for Experimental Steps and other Objects
URL: https://datastore.bam.de/en/How_to_guides/Templates
Source: datastore
---

/
How_to_guides
/
Templates
Create Templates for Experimental Steps and other Objects
Last edited by
<PERSON><PERSON><PERSON><PERSON>, Caroline
07/11/2025
To create a Template for an Object type, navigate to left main menu and select
Utilities
, and then
Settings
. A Select Group Settings drop-down menu will appear, select your
division number
to open your group settings. Click on the
Edit
tab, open the Templates section, click on the
+ New Template
tab. Select an Object Type for which the template is intended, e.g. Experimental Step. Fill out the predefined values as required, review the entries and
Save
.  All available templates to your group are displayed in the Templates section.
Note, using a Template will overwrite the existing parent(s) and child(ren) defined in the registration form of the
Object
. When registering
sequential Experimental Steps
using a Template, you must
explicitly specify the parent(s) and/or child(ren) during the registration
process. If the parent(s) and child(ren) are fixed for a particular Template, they can be defined at the time of the Template's creation. In such cases, they will be automatically applied whenever the Template is used.
Note that to quickly visualise information and preview text, images or tables in the Object form, you can embed this content when creating a Template for Experimental Steps and other Objects. To display the preview, select the corresponding Project or Collection from the menu on the left and click on the icon in the Document column in the Collection form.
Under Utilities
Select Settings
Select division number
Click on Edit tab
Open Templates section
Click on + New Template tab
Select an Object type- Experimental Step
Fill out the form with the pre-defined values
Review the entries and Save.