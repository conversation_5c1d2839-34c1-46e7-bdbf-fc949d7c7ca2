Title: Share Code in BAM research GitHub
URL: https://datastore.bam.de/en/How_to_guides/Share_code
Source: datastore
---

/
How_to_guides
/
Share_code
Share Code in BAM research GitHub
Page Contents
✅ Prerequisites
Two options to Share Your Code
Option A: Create a New Repository
Option B: Copy an Existing Repository
🧩 Need Help?
Last edited by
<PERSON><PERSON><PERSON><PERSON>, Caroline
07/01/2025
This guide walks you through the steps to share your code (scripts, parsers, tools, etc.) in the
BAM research GitHub organization
, following
open-source best practices
.
¶
✅ Prerequisites
You have a GitHub account.
You are a member of the
BAM research GitHub organization
.
For access please contact
<EMAIL>
via Microsoft Teams with your GitHub username to request an invitation.
¶
Two options to Share Your Code
¶
Option A: Create a New Repository
Go to the
BAM research GitHub organization
.
Click
"New repository"
.
Fill in the repository details:
Name
: Choose a clear, descriptive name.
Description
: Briefly explain what the code does.
Visibility
: Choose
Public
(preferred for OSS) or
Private
.
Click
Create repository
.
¶
Option B: Copy an Existing Repository
If your code already exists elsewhere:
Fork it
:
If the original repository is public and has a compatible license:
Go to the original repo and click
Fork
→ Choose
BAM research
as the destination.
Duplicate it
:
If forking isn’t suitable:
Clone the original repo locally.
Create a new repo under BAM research.
Push the code to the new repo.
¶
🧩 Need Help?
If you’re unsure about licensing, repository setup, or GitHub workflows, reach out to Data Store Team at
<EMAIL>
.