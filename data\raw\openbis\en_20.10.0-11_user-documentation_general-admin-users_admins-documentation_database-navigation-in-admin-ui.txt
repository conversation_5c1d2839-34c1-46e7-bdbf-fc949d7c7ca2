Title: Database navigation in admin UI
URL: https://openbis.readthedocs.io/en/20.10.0-11/user-documentation/general-admin-users/admins-documentation/database-navigation-in-admin-ui.html
Source: openbis
---

Database navigation in admin UI

openBIS version 20.10.5 provides a new database navigation in the admin
UI, as shown below.
This allows to navigate the openBIS hierarchy of
Spaces, Projects,
Experiments/Collections, Objects, Datasets
.
The same navigation menu will be used in the ELN UI in an upcoming
openBIS version.
Features

Filter

It is possible to filter the menu by code or name of the desired entity.
Navigation

To navigate the menu, the nodes have to be opened individually.
When you select an entry in the node, the corresponding entry page
opens. Please note that at this stage, this is only intended as a
preview for the navigation menu and the forms show the information  of
the entity in json format.
If you open several entry pages and you switch between them, by default
the navigation menu will scroll up or down to the corresponding entry in
the menu. If you do not want to have this behaviour, you can turn off
the scrolling by selecting the button on the top right corner of the
menu, as show in the picture below.
Sorting

Each level of the hierarchy except for
Spaces
(
Projects, Collections,
Objects, Datasets
) can be sorted separately, using the buttons shown in
the pictures below.
By default, the sorting is done by
Code
in alphabetical order. Other
options are: sorting by code in reverse alphabetical order; sorting by
ascending date; sorting by descending date.
Updated on December 5, 2022