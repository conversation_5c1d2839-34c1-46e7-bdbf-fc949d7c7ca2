Title: Represent research data
URL: https://datastore.bam.de/en/How_to_guides/Represent_research_data
Source: datastore
---

/
How_to_guides
/
Represent_research_data
Represent research data
Page Contents
Conceptual data Model
1. Draw the research data workflow
2. Identify Entities and Entity types
3. Map Entities in the Data Store-openBIS data structure
4. Connect Entities
Last edited by
<PERSON><PERSON><PERSON><PERSON>, <PERSON>
08/11/2025
¶
Conceptual data Model
To map research data in the Data Store – openBIS Research Data Management system, data are stored together with all experimental steps, tangible and intangible objects (things you do, generate and have) that are relevant to generate traceable and reusable data.
It is advisable that divisions create a conceptual data model to have a visual tool that supports the discussions and development of the data model.  In BAM, the DSSt leads the design of the data model and coordinates the feedback rounds with the division members.  An understandable data model for the division supports effective data management, analysis, and collaboration. It is recommended to understand the concepts from the beginning to improve and expand the data model according to the needs of the department.
The following steps serve as a guide for creating a conceptual data model. Any visualization tool can be used, the
template
in
draw.io
can be downloaded and reused to implement the following steps.
¶
1. Draw the research data workflow
Identify the data (of any format, single or multiple files, datasets) to be stored in the Data Store.
Add the
things you do
(e.g., synthesis, measurements, analysis, etc.) to generate the data and specify them as Experimental Steps.  Attach data to Experimental Steps.
Connect the Experimental Steps with unidirectional arrows to indicate the logically occurrence and dependency.
Add the things you generate,
tangible
or
intangible
(Samples, Materials, etc.).
Add the
things you have
or
need
to complete all Experimental Steps and that are relevant to generate traceable and reusable data. Use general categories such as Chemicals to simplify visualization rather than listing individual chemicals.
To simplify the flowchart, make sure all Experimental Steps have at least one dataset attached (otherwise, check whether the Experimental step is part of another).
¶
2. Identify Entities and Entity types
Identify all
Entities
used (Chemical 1, Chemical 2) or generated (Samples) in each Experimental Step.
Group similar Entities under common
Entity Types
(chemicals 1, 2 in Chemicals and Nanoparticles in Samples). Several Entity types have already been defined by BAM users, try to reuse them if possible. Use the
MASTERDATA CHECKER
to identify existing Entity Types and their properties in the Data Store. To add Properties to an Entity Type or to define new Entity Types, the DSSt(s) can contact the Data Store team at
<EMAIL>
.
If an Entity cannot be grouped with others, list it in the table and leave the Entity Type name blank. Contact the Data Store team to find out how to represent this Entity in the Data Store.
Note that generated things in an Experimental Step such as Code and Data, can be uploaded to the system as datasets.  These datasets can be uniquely described by defining an Entity Type or be uploaded with minimal metadata as generic Datasets with default properties (e.g., dataset name) defined by the system. If no Entity Type is defined for Code or Data, all relevant Information should be stored within the Experimental Step used linked to these items.
¶
3. Map Entities in the Data Store-openBIS data structure
To map Entities in the openBIS organize Entities at Object level within the hierarchical
data structure
of openBIS: Space → Project → Collection → Object → Dataset. Consider the pre-defined organization of the BAM (public) and FB (private) -Inventory and Lab Notebook Spaces.
Map Entities in the openBIS data structure.
List all Objects at the Object level to represent the things you do and the things you generate in the ELN. Organize all the things you have in the Inventory.
Assign meaningful names to Collections. Collections can contain one or more Objects of one or more Types (Entity Types).
¶
4. Connect Entities
To connect research data in openBIS, define unidirectional
parent–child relationships
. These connections can be defined Object to Object or Dataset to Dataset across Spaces, regardless of whether they are in the Inventory or in the Lab Notebook. For the Data Store, it is recommended to connect Objects to Objects:  openBIS provides an automatic visualization of Objects. To visuaize and understand data, all Objects should be connected to each other.
Visualize the connections between Objects
.
To define the connections between Objects, draw unidirectional arrows between Objects. By default, each Object can have an unlimited number of parents and/or children or none (N:N relationships with N being any number from 0 to N). For a specific Object type, the DSSt (group admins) can define a maximum number of children and parents.