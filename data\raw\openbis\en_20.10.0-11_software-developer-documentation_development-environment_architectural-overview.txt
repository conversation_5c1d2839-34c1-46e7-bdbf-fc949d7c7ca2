Title: Architectural Overview
URL: https://openbis.readthedocs.io/en/20.10.0-11/software-developer-documentation/development-environment/architectural-overview.html
Source: openbis
---

Architectural Overview

Repository organization

The repository contains these kind of modules used to build the openBIS distributable:
api-*: API Facades
app-*: Applications
build: Build scripts
core-plugins-*: Core plugins distributed with openBIS
lib-*: Internally maintained libraries used to build openBIS
server-*: Server components
test-*: Integration tests
ui-*: User interfaces