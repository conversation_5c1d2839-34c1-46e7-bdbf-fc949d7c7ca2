Title: Properties handled by <PERSON>ripts
URL: https://datastore.bam.de/masterdata_definition_files/20250730_masterdata_of_controlled_vocabulary_dfg_device_code.xlsx
Source: datastore
---

/
Previous_version_of_Wiki
/
datastore
/
stewards
/
properties-handled-by-scripts
Properties handled by Scripts
Dynamic Property and Entity Validation Scripts
Page Contents
Dynamic Property and Entity Validation Scripts
Dynamic Property Scripts
Entity Validation Scripts
Last edited by
<PERSON><PERSON><PERSON><PERSON>, Caroline
08/04/2025
¶
Dynamic Property and Entity Validation Scripts
openBIS offers two different options to include custom Jython/Python scripts as part of the Masterdata:
Dynamic Property Scripts
are a mechanism to automatically compute values of
Properties
that should not/cannot be manually modified by users.
Entity Validation Scripts
are a mechanism to ensure metadata consistency of an entity type (
Collection
,
Object
, or
Dataset
type).
Both types of scripts are defined in the openBIS Admin User Interface (UI) under "Tools" -> "Dynamic Property Plugin" or "Entity Validation Plugin", respectively. Alternatively, they can be imported as .py files as part of the Masterdata ZIP folder when using the Excel import option.
¶
Dynamic Property Scripts
In most cases, values of
Properties
are defined by users in the ELN-LIMS UI or via pyBIS. In contrast, Dynamic Property Scripts can be used to automatically compute values of so-called dynamic
Properties
that should not/cannot be manually modified by users. The script defines a function that returns a value for a dynamic
Property
, usually based on the values of one or more
Properties
of the same entity.
Dynamic Property Scripts are part of the
Property
type assignments of entity types. This means that the script is not always used for a certain
Property
type. Instead, it is one of the optional characteristics of a
Property
type that is assigned to a specific entity type: The same
Property
type can be a dynamic
Property
(and have a Dynamic Property Script) for entity type A, while for entity type B, it is a normal
Property
that has to be filled by the user.
¶
Example for Dynamic Property Scripts
The
Object
type RECTANGLE includes the following three
Property
types:
RECTANGLE_LENGTH_IN_M
[REAL]
RECTANGLE_WIDTH_IN_M
[REAL]
RECTANGLE_AREA_IN_QM
[REAL]
Only the first two
Properties
can be edited by users in the ELN-LIMS UI. Once the
Object
is saved, the value of the
Property
RECTANGLE_AREA_IN_QM
is automatically computed as the product of the values of
RECTANGLE_LENGTH_IN_M
and
RECTANGLE_WIDTH_IN_M
as defined in the Dynamic Property Script "rectangle_area":
def
calculate
(
)
:
"""Main script function. The result will be used as the value of appropriate dynamic property."""
length
=
entity
.
propertyValue
(
"RECTANGLE_LENGTH_IN_M"
)
width
=
entity
.
propertyValue
(
"RECTANGLE_WIDTH_IN_M"
)
area
=
length
*
width
return
area
Copy
When assigning the
Property
type
RECTANGLE_AREA_IN_QM
to the
Object
type RECTANGLE, the name of the script is entered in the field "Dynamic Property Plugin" in the Admin UI:
When using the Excel import option, the name of the script is entered as "rectangle_area.py" in the column "Dynamic script" in the Masterdata Excel file.
The script itself is defined (and can be tested) in the openBIS Admin UI under "Tools" -> "Dynamic Property Plugin" (see screenshot for Entity Validation Scripts
above
). Alternatively, it can be imported as a .py file as part of the ZIP folder that also contains the Masterdata Excel file(s).
¶
Rules & Best Practices for Dynamic Property Scripts
The script must be written in Jython/Python 2.7. No additional modules may be used, only the
Python Standard Library
.
The computation of the value of the dynamic
Property
must be based on the values of one or more
Properties
of the same entity.
Properties
of other entities must not be accessed.
All information needed for the calculation must be included in the script. External resources must not be accessed.
Dynamic Property Scripts should be simple and should not include any performance-heavy functions, since they are executed whenever the entities to which they are assigned are created or updated.
The script must be added when first creating the
Property
type or, in the case of an existing
Property
type, when assigning it to an entity type. It cannot be added retrospectively after the
Property
type assignment already exists.
¶
Entity Validation Scripts
Entity Validation Scripts are defined at the entity type (
Collection
,
Object
, or
Dataset
type) level.
Once an entity of a certain type is created or modified, the validation is perfomed according to the custom rules defined in the script. If the validation fails, the operation (entity creation or modification) is aborted and an error message is returned.
¶
Example for Entity Validation Scripts
An example of an Entity Validation Script is the
Date Range Validation
("EXPERIMENTAL_STEP.date_range_validation") which checks for an
Object
of the type EXPERIMENTAL_STEP whether the date entered for the
Property
END.DATE
is later than the date entered for the
Property
START.DATE
.
If not, the error message "End date cannot be before start date!" is returned and the EXPERIMENTAL_STEP cannot be saved.
#date_range_validation.py
def
getRenderedProperty
(
entity
,
property
)
:
value
=
entity
.
property
(
property
)
if
value
is
not
None
:
return
value
.
renderedValue
(
)
def
validate
(
entity
,
isNew
)
:
start_date
=
getRenderedProperty
(
entity
,
"START_DATE"
)
end_date
=
getRenderedProperty
(
entity
,
"END_DATE"
)
if
start_date
is
not
None
and
end_date
is
not
None
and
start_date
>
end_date
:
return
"End date cannot be before start date!"
Copy
¶
Rules & Best Practices for Entity Validation Scripts
The script must be written in Jython/Python 2.7. No additional modules may be used, only the
Python Standard Library
.
The validation must be based on the values of one or more
Properties
of the entity being validated.
Properties
of other entities must not be accessed for validation.
All information required for validation must be included in the script. No external resources may be accessed for the validation.
Entity Validation Scripts must be read-only. No
Properties
of the entity may be added or edited.
Entity Validation Scripts should be simple and should not contain any performance-heavy functions, since they will be executed every time entities of this type are created or updated.