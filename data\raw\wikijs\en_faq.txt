Title: Frequently Asked Questions (FAQ)
URL: https://datastore.bam.de/en/faq
Source: datastore
---

/
faq
Frequently Asked Questions (FAQ)
Page Contents
General
IT Infrastructure
Data Formats
Metadata
Traceability
Data Analysis
Data Import/Export
Interfaces
Development
Rollout
Pilot Phase
Last edited by
<PERSON><PERSON><PERSON>, Kristina
06/17/2025
¶
General
Who can use the Data Store? Is the use of the Data Store mandatory?
In the future, all BAM employees who work with research data will be able to use the Data Store. Divisions that have registered for the rollout commit themselves to using the Data Store after the end of the onboarding phase. It is the responsibility of the division head to ensure that the Data Store is used appropriately.
Who is responsible for maintaining the Data Store?
The Data Store is operated as a central service by the central IT (VP.2). Training and consulting is provided by the eScience section (VP.1). The divisions themselves are responsible for the content of their group and maintenance of metadata and data.
What kind of data should be stored and what data should NOT be stored in the Data Store?
The Data Store is primarily a system for the internal storage of research data produced at BAM. According to the BAM Data Policy, research data include all digital data that are the basis, object, work steps or result of research processes as well as data that serve to describe the former. Typical examples of research data are measurement and observation data, experimental data obtain in the laboratory, audiovisual information, methodological test procedures and protocols. Simulations, software source code, algorithms, and derivations are also research data that can be stored in the Data Store. However, since the Data Store does not include a version management system, it is recommended to use a more suitable service for software development projects, for example the
BAM GitHub
(more information can be found
here
in the Infoportal). It is not allowed to store private data, in particular no personal data, in the Data Store. If you are not sure whether your data may be stored in the Data Store, please contact
<EMAIL>
.
How is the Data Store organized?
The Data Store is divided into groups that correspond to the BAM divisions. By default, each group receives its own Inventory for the digital representation of laboratory inventory such as measuring instruments, chemical substances, samples, protocols, etc., as well as associated documents (e.g. technical data sheets). By default, each user additionally receives their own Lab Notebook with personal folders for the documentation of research activities and related data. If required, project-based folders (for several project members) can also be created in the Lab Notebook in addition to the personal folders.
Can closely collaborating divisions have common Spaces in the Data Store Inventory?
Each division is assigned open Spaces (readable by all Data Store users) and closed Spaces (readable and editable only by division members) in the Data Store Inventory. Group Admins can grant read/write access to the Spaces to members of other divisions to enable joint work. If required, dedicated shared Spaces can be created as well.
How are access rights defined within the Data Store?
The Data Store is divided into groups, which correspond to BAM divisions. Within a group, roles can be assigned and access rights can be further defined. Users with the appropriate rights can also grant specific access to Spaces and Projects to non-group members. More information about the rights and role system in the Data Store can be found
here
.
Can changes in the BAM organization (e.g. merging of divisions) be represented in the Data Store?
The user accounts in the Data Store as well as the organizational affiliation of the users are derived from the central user directory of BAM (LDAP). If your division is affected by upcoming organizational changes (e.g. renaming or merging) and if the division is  already using the Data Store, please contact
<EMAIL>
.
Is the Data Store/the underlying software openBIS multilingual?
No, the user interface of openBIS as well as the official documentation of the ETHZ are only available in English. Training materials for the Data Store are also provided in English. If you need a translation of certain documents/articles, please contact <NAME_EMAIL>. Except for justified exceptions, we also recommend documenting user-defined metadata in openBIS exclusively in English to ensure better interoperability.
Is the Data Store also suitable for the management of data generated in the context of testing tasks/scientific and technical services (Wissenschaftlich-Technisches Dienstleistungen, WTD)?
In principle, any type of data can be stored in the Data Store and described with metadata. Whether the Data Store is also suitable for managing test data will be investigated during the rollout.
My data files are very large, can the Data Store handle them?
The actual data files are stored in the Data Store as files on disk storage, only the metadata is stored in the database. This means that even very large data volumes are possible, as long as the corresponding storage resources are available. In addition, there is the possibility via git-annex in openBIS to manage only references to datasets when they become too large.
¶
IT Infrastructure
What happens to the existing file services after the introduction of the Data Store?
The personal file services (drive "M:") will still be provided. The necessity of shared file services by divisons, project groups, or departments will be reviewed. If required, they will be provided as a supplement to the Data Store.
Where is the data stored in the Data Store physically located? Is there be a backup of the Data Store?
All data of the Data Store are exclusively stored on servers in the computing center of BAM (UE). The datasets/metadata are backed up regularly/continuously in a multi-stage process.
Will the Data Store be permanently available?
As a central RDM system, the Data Store is designed for permanent operation as far as this is technically possible. However, it will not be possible to avoid interruptions  for maintenance work and updates.
Will there be access to the Data Store from the lab networks ("Labornetze")?
Yes, access from laboratory networks will be enabled where necessary.
Will there be access for external parties to the Data Store?
No, the Data Store is designed as a system for internal research data management at BAM and requires access from internal BAM networks (including VPN). Therefore, external users cannot access the Data Store. However, it is possible to export data from the Data Store, e.g., to the public repository
Zenodo
.
¶
Data Formats
Which file formats can be stored in the Data Store?
openBIS works independently of file formats: The data are stored as files in the file system, the associated metadata in a database.
Can I read/work with proprietary file formats in the Data Store?
The Data Store is not a live environment for reading or editing files, independent of whether their format is open or proprietary. To read or edit files stored in the Data Store using specialized software, they must be downloaded locally. The modified files can then be re-saved in the Data Store and linked to the original dataset. If the underlying file format allows programmatic access, files stored in the Data Store can also be read and analyzed via APIs (for example, with the Python module
pyBIS
and Jupyter Notebook), but not modified.
¶
Metadata
What kind of metadata standards are used in the Data Store?
In the Data Store, individual metadata standards can be defined depending on the research domain. Some basic standards are already offered (e.g. for the description of instruments, chemicals and experimental steps). The definition of additional (domain) metadata standards is an important part of the onboarding process. The divisions are supported in this task by the Data Store team.
Can ontologies be represented in the Data Store?
There is currently no function to import ontologies to openBIS. However, it is possible to add so-called semantic annotations when defining metadata Object and Property Types. However, these are currently not visible in the UI and can only be accessed via API.
¶
Traceability
Can I edit data once it is stored in the Data Store?
In the Data Store, there is a clear technical seperation between data (in the form of files) and descriptive metadata. Files are stored in folders, so-called Datasets, which can contain one or more file(s) and whose content is immutable and stored on disk storage. However, Datasets be deleted in their entirety. Metadata entities, on the other hand, are stored in a separate database and can be both edited and deleted.
Can edits of (meta)data be tracked in the Data Store?
All edits to the metadata in the Data Store are saved (which change was made when and by whom), so that the entire history of a metadata entry can be traced back if necessary. Files stored in the Data Store, on the other hand, are immutable: They cannot be edited after they have been saved, but they can be deleted.
Can metadata/data stored in the Data Store be deleted and can deleted metadata/data be recovered?
Users with appropriate rights can edit entities as well as delete metadata Objects and associated files (Datasets). Deleted Objects and Datasets are first moved to the openBIS trashcan from where they can be restored if necessary. If Objects/Datasets are removed from the trashcan, they are permanently deleted from the underlying database and cannot be restored. By default, only users with Space Admin and Instance Admin role have permission to delete. If you want to completely prevent editing/deleting an entity, you can irreversibly "freeze" individual Objects or Datasets,  as well as entire folders in the Data Store.
Can chronological relationships be represented in the Lab Notebook of the Data Store?
Yes, by means of directed links between Objects, so-called "parent-child relationships", the chronological sequence of, e.g., several experimental steps can be represented in a hierarchy tree. You can find more information about parent-child relationships
here
.
¶
Data Analysis
What are Jupyter Notebooks and how can they be used to analyze data in the Data Store?
Jupyter Notebook
is a web-based interactive computing platform that combines live code, equations, narrative text, visualizations etc. The Jupyter system currently supports over 100 programming languages including Python, Java, R, Julia, Matlab, Octave and many more. Jupyter Notebooks can be used to analyze data stored in an openBIS instance, e.g., by connecting a local Jupyter installation with the Data Store. The output of the analysis and the notebooks themselves can then be saved in the Data Store and connected to the dataset they are based on. It is possible to download an
extension for JupyterLab
that adds three buttons to a default notebook to
connect to an openBIS instance,
download datasets from the openBIS instance,
upload the notebook to the openBIS instance. For researchers using Python for data analysis, we recommend to use
pyBIS
, a Python module for interacting with openBIS, which is designed to be most useful in a Jupyter Notebook.
¶
Data Import/Export
What are the upload options to the Data Store?
Files can be uploaded to the Data Store in several ways:
via the graphical user interface (GUI),
via script, e.g., via the Python module
pyBIS
,
via the Dropbox mechanism, where files are copied to a specially created Dropbox folder.
You can find more information about the upload via GUI
here
.
Will there be support for implementing Dropbox scripts for automated data import?
It is planned to develop a "meta dropbox script" that offers a number of core functionalities (e.g. search for metadata Objects in the Data Store to which the datasets are to be attached; create new Objects and set metadata properties) as well as a template for entering the required metadata. The division must fill this template with the appropriate metadata: either manually or automatically via a parser script that is tailored to a specific data format.
Can warnings or events be triggered when importing data via Dropbox?
Yes, the Dropbox scripts can validate incoming data and act accordingly.
Can continuous data streams from measuring devices be included in the Data Store?
No, openBIS works with files only. Continuous data streams must be split into individual data files (e.g. per week, day, hour) which can be saved in openBIS as immutable data sets. The integration of openBIS and measuring devices is possible via the so-called Dropbox mechanism. For this, the data files must be saved in a dedicated Dropbox folder; from there they are uploaded to the Data Store. The Dropbox can be controlled via scripts that contain the logic for the data upload. You can find more information
here
.
Can data be exported/published from the Data Store?
Yes, files and descriptive metadata can be exported, both locally as well as to the public research data repository
Zenodo
.
¶
Interfaces
What interfaces (to devices and software) does the Data Store offer?
The APIs of openBIS are described
here
in the openBIS documentation of the ETHZ. There are programming interfaces to Java, Javascript and Python. For data analysis, there is the possibility to connect a local Jupyter installation to the Data Store (Jupyter Notebook, Hub and Lab). Jupyter itself provides numerous kernels to support a variety of programming languages for analyzing data in openBIS.
Will there be an interface between the Data Store to the hazardous substances database sigmaBAM in order to avoid duplicate work when entering hazardous substances?
There is currently no interface between the Data Store and sigmaBAM. Although both systems are intended for the digital representation of chemicals or hazardous substances, the perspective differs: sigmaBAM is used for the documentation of handling permits for hazardous substances as well as for a yearly updated total quantity of a hazardous substance. In the Data Store, on the other hand, it is recommended to represent the specific batch of a chemical and link it to experimental steps to ensure traceability. However, the metadata format for chemicals in the Data Store is based on the metadata format of hazardous substances in sigmaBAM. For divisions that have already entered hazardous substances in sigmaBAM, it is therefore possible to export these as Excel lists and import them (after some adjustments) into the Data Store.
Will there be an interface from the Data Store to the E-Akte (or vice versa)?
At the moment, there are no direct interfaces between the Data Store and the E-Akte, because the two systems are used to store different kinds of data (research data in the Data Store vs. (other) record-relevant documents in the E-Akte). If necessary, files in the E-Akte can be hyperlinked in the Data Store.
Can data stored in the Data Store be linked to publications in Publica?
There are currently no interfaces between the Data Store and Publica. However, it is possible to reference publications in the Data Store, e.g. via hyperlinks to their DOIs.
¶
Development
I have an idea for a new/improved openBIS feature or plugin for the Data Store. Where can I submit this?
Please send your ideas for improved and/or additional <NAME_EMAIL>. Depending on whether your idea is a BAM-specific or a general openBIS feature, we will include your suggestion in our feature request list or forward it to the openBIS development team at ETHZ. Please note, however, that due to limited development resources we cannot implement every feature request and therefore prioritize them according to effort/added value. Since openBIS is an open source software, the development team at ETHZ also handles openBIS feature requests at its own discretion.
Can I develop my own plugins for the Data Store?
If you would like to participate in the development of plugins for the Data Store, please contact
<EMAIL>
.
¶
Rollout
When will the rollout of the Data Store begin?
The rollout of the Data Store takes place in phases. In each phase, several divisions will be onboarded to the Data Store at the same time. The first rollout phase started in May 2023.
How can I register my division for the rollout?
All division heads were contacted by the project team in December 2022 as part of a survey to gauge their interest in rollout participation. Based on the responses, the rollout sequence for the first three phases was determined. In February 2025, another survey was conducted, which will now serve as the basis for further phase planning. The divisions involved in phase 4 have already been contacted. If you are a head of division and did not receive the survey, or if you wish to change your response, please contact us at
<EMAIL>
.
What is a Data Store Steward/Group Admin and what are the responsibilities associated with the role?
Before the Data Store can be used operationally, research group-specific data structures and metadata schemas have be developed. To this end, at least one Data Store Steward must be appointed from each division to take on this task in consultation with colleagues and the division head. The Data Store Stewards are trained and supported in their work by the Data Store project team. Once the Data Store has been implemented for a division, the Data Store Steward acts as Group Admin. Group Admins can access all data and metadata within their own group and (in the case of metadata) edit it, as well as adjust access rights and other settings to meet the requirements of the division. Data Store Stewards are also the point of contact for research data management (RDM) issues within the division and are responsible for introducing new employees to the Data Store once onboarding has been completed. Data Store Stewards should be familiar with methodology and processes within the division and its research domain. Prior IT and RDM experience is helpful, but not required. Ideally, Data Store Stewards should have permanent positions in the division to reduce the risk of knowledge loss upon departure. For larger divisions with subgroups, it is recommended that at least two Data Store Stewards be assigned.
I would like to take on the role of Data Store Steward/Group Admin for my division, where can I apply for this?
To do so, please talk first with the head of your division and then contact us at
<EMAIL>
.
Will there be training and/or manual on how to use the Data Store?
Yes, during onboarding, target group-specific training (for Data Store Stewards and for normal users) will take place. In addition to the openBIS documentation of the ETHZ, accompanying training material will be provided here in the Data Store Wiki.
How much time should a division invest for the rollout?
A rollout phase lasts approx. 2-3 months. During this time, onboarding events are held for the Data Store Steward(s) and for the members of the division. The effort required for adjustments to the Data Store and, if necessary, the setup of interfaces depends on the research subject, the equipment, the working methods, and the FDM requirements of the department and cannot be specified in general terms. The obligations of DSSts include carrying out the onboarding process (approx. 5 working days) and scheduling additional time for system adjustments, as well as integration into daily workflows after onboarding.
How can I/my division prepare for the rollout?
In preparation for the rollout (and for good research data management in general), it is recommended to consider the following points:
How to define uniform rules for naming files within the division (e.g., YYYYMMDD_someMeasurement_Initials_v1.csv)?
What kind of entities should be represented in the Inventory of the Data Store? Can the properties of these entities be structured as standardized metadata in lists? These lists can later be customized and imported into the Data Store.
What kind of information is needed in a typical experiment description? Can this also be standardized in the form of metadata? Can templates for experiment descriptions be created?
What are the links between experimental steps and inventory elements (e.g. an experimental step should be linked with the measuring instrument that was used)?
What kind of output data formats are generated by measurements? Are they open or proprietary (can only be read with special software from the manufacturer)? If open, what kind of measurement metadata are included? Is it possible to write a parser in the form of a short script that automatically reads this metadata?
Will we get additional staff for the rollout?
Unfortunately not, the subject-specific implementation and customization of research data management in the Data Store is to be conducted by the divisions themselves. Each division must appoint one or more Data Store Steward(s) for this task. However, the Data Store team from the sections VP.1 (eScience) and VP.2 (IT) will closely accompany the onboarding process and support the Data Store Stewards and the users by offering webinars, Q&A sessions and training materials.
Can I test openBIS before my division is onboarded to the Data Store?
Three demo openBIS instances of ETHZ and EMPA are available
here
.
In future, we will provide a demo instance of the Data Store. If you are interested in testing it, please contact the Data Store team at
<EMAIL>
.
The openBIS installer as well as a pre-installed virtual machine image is available
here
. For the download, a registration at the SIS helpdesk is necessary. A
container image
is also provided. It should be noted, however, that the entire functionality only becomes visible after a complex configuration process and not all features can be easily grasped in a demo installation.
Can I participate in the Data Store rollout as an individual employee?
No, the rollout of the Data Store is designed for divisions and not for individual employees.
¶
Pilot Phase
My research group was part of the pilot project. What happens to the pilot instance during the rollout?
As agreed at the beginning of the pilot project, the pilot instances will continue to be supported, provided with security updates and kept operational, but not provided with new features (which go beyond the regular openBIS updates) or further customised. In the medium term, it is planned to transfer the data of the pilot instances to the BAM-wide system.
Will more pilot groups be onboarded as test cases for the Data Store?
No, the pilot project was completed in February 2022 and no additional pilot groups will be included. All further groups will be included in the rollout, i.e. the central implementation of the Data Store, which started in May 2023.