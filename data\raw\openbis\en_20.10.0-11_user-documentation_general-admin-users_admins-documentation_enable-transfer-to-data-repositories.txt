Title: Enable Transfer to Data Repositories
URL: https://openbis.readthedocs.io/en/20.10.0-11/user-documentation/general-admin-users/admins-documentation/enable-transfer-to-data-repositories.html
Source: openbis
---

Enable Transfer to Data Repositories

Currently openBIS offers an integration with the
Zenodo
data
repository (
https://zenodo.org/).
This enables data direct data transfer from openBIS to Zenodo.
This feature needs to be configured on
system level
as explained
here:
openBIS DSS configuration
file
.
If this is done, the Zenodo Export needs to be made visible in the ELN
UI by a lab manager, who has should have admin rights for the
Settings
. This can be done by a
group admin
, in case of a
multi-group instance set up.
Procedure:
Edit the
Settings
under
Utilities.
Select
showZenodoExportBuilder
in the
Main Menu
section.
Save.
The
Export to Zenodo
functionality becomes available under the
Utilities
menu (a refresh of the browser page may be necessary to
see the change):
Updated on April 26, 2023