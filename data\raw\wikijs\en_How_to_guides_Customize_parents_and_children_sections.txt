Title: Customize Parents and Children sections in Object Forms
URL: https://datastore.bam.de/en/How_to_guides/Customize_parents_and_children_sections
Source: datastore
---

/
How_to_guides
/
Customize_parents_and_children_sections
Customize Parents and Children sections in Object Forms
Last edited by
<PERSON><PERSON><PERSON><PERSON>, Caroline
06/10/2025
In the left main menu, under
Utilities
select
Settings
. The Select Group Settings drop-down menu will appear. Select your
division number
to open your group settings. Click on the
Edit
tab, navigate to the
Object type definitions
Extension section and select the
Object Type
you want to customize. To deselect sections for Parents or Children, select the
Disable section
checkbox, to disable the addition of any Object type as Parent or Child, select the disable addition of any object type) checkbox.
To make adding Parents and Children more convenient to users, enter a name under Section name (e.g., Parents - Section name: Instruments for Experimental Step).  Review the changes and Save.
Under Utilities
Select Settings
Select division number
Click on Edit tab
Navigate to Object type definitions Extension section
Select an Object type
If required: Select Disable section, Disable addition of any object type checkboxes
Enter Section name for Parents and Children
Review changes and Save.